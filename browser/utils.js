// 放置产品逻辑无关的工具函数
const fs = require("fs");

/**
 * 获得今天的日期 yyyy-mm-dd格式
 */
function getToday() {
  return new Date().toISOString().slice(0, 10);
}

function readFileSync(filePath) {
  try {
    // 同步读取文件
    return fs.readFileSync(filePath, 'utf8');
  } catch (err) {
    return "" // ignore
  }
}

/**
 * 清理文件，只保留最近的N行
 * @param filePath 要清理的文件路径
 * @param reserveLines 要保留的最近的N行
 */
function clearFile(filePath, reserveLines) {
    fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          console.error('Error reading file ' + filePath + ': ', err);
          return;
        }
        const lines = data.split('\n');
        if (lines.length <= reserveLines) {
          return; // 行数不超过保留行数，不需要清理
        }
        const newData = lines.slice(-reserveLines).join('\n');
        fs.writeFile(filePath, newData, (err) => {
          if (err) {
            console.error('Error writing file ' + filePath + ': ', err);
          }
        });
    });
}

/**
 * 监听文件变化，当文件不存在时，会创建文件
 * @param filePath 监听的文件路径
 * @param callback 回调函数，参数为文件的完整数据
 */
function watchFileChange(filePath, callback) {
  function watchFile() {
    fs.watch(filePath, (event, filename) => {
      if (event === 'change') {
        fs.readFile(filePath, 'utf8', (err, data) => {
          if (err) {
            console.error('Error reading file ' + filePath + ': ', err);
            return;
          }
          callback(data);
        });
      }
    });
  }

  // 如果文件不存在，先创建文件
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      fs.writeFile(filePath, '', (err) => { // 文件不存在，创建文件
        if (err) {
          console.error('Error creating file ' + filePath + ': ', err);
          return;
        }
        watchFile();
      });
    } else {
      watchFile();
    }
  });
}

function parseLastNonEmptyLine(text) {
  const lines = text.split('\n');
  for (let i = lines.length - 1; i >= 0; i--) {
    const line = lines[i].trim();
    if (line !== '') {
      return line; // 返回第一个非空行
    }
  }
  return null; // 如果文本全为空行，则返回null
}

module.exports = { getToday, readFileSync, watchFileChange, parseLastNonEmptyLine, clearFile };