const { app, BrowserWindow, Tray, Menu } = require('electron');
const common = require('./common.js');
const utils = require('./utils.js');
const path = require('path');

const gotTheLock = app.requestSingleInstanceLock(); // 单例浏览器模式
if (!gotTheLock) {
  app.quit();
} else {
  app.setPath ('userData', process.cwd() + "/userData"); // 设置浏览器的用户数据存储路径

  function createWindow() {
    let domReady = false
    const win = new BrowserWindow({ // 创建浏览器窗口
      width: 800,
      height: 600,
      show: false, // 首先隐藏窗口以避免闪烁
    });

    let isTrayExist = false;
    const tray = new Tray(path.join(__dirname, 'trayIcon.png'));
    const contextMenu = Menu.buildFromTemplate([
      { label: 'Open', click: () => win.show() },
      { label: 'Quit', click: () => {isTrayExist=true; app.quit(); } }
    ]);
    tray.setToolTip('ChatGPT');
    tray.setContextMenu(contextMenu);

    tray.on('click', () => {
      if (!win.isVisible()) {
        win.show();
      }
    });

    win.on('close', (event) => {
      if (!isTrayExist) {
        event.preventDefault();
        win.hide();
      }
    });

    common.onSecondInstanceStart(app, win); // 第二个实例启动时，聚焦到到第一个实例
    win.maximize(); // 最大化窗口
    win.loadURL(common.getChatGPTUrl());
    win.webContents.on('dom-ready', () => {
      domReady = true;
    });
  }

  app.whenReady().then(createWindow); // 当 Electron 完成初始化并准备创建浏览器窗口时触发

  app.on('activate', () => { // 在应用程序激活时（例如点击 dock 图标），创建浏览器窗口
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });

  common.onCloseBrowser(app);
}
