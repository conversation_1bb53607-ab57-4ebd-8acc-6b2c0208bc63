// 放置通用的产品逻辑相关的代码
const os = require('os');
const utils = require('./utils');
const fs = require("fs");
const tempDir = os.tmpdir();

const defaultUrl = "https://www.google.com";
function getChatGPTUrl() {
    try {
        const configBody = fs.readFileSync("config.json", 'utf8')
        const config = JSON.parse(configBody);
        return config.url || defaultUrl;
    } catch (error) {
        console.log("read config file error: ", error);
        return defaultUrl;
    }
}

/**
 * 当所有窗口关闭时退出应用
 */
function onCloseBrowser(app) {
    app.on('window-all-closed', () => { // 在所有窗口都关闭时退出应用（除非在 macOS 上按下 Command + Q）
        if (process.platform !== 'darwin') {
            app.quit();
        }
    });
}

/**
 * 当第二个实例启动时，聚焦到到第一个实例
 */
function onSecondInstanceStart(app, win) {
    app.removeAllListeners('second-instance');
    app.on('second-instance', (event, commandLine, workingDirectory) => { // 监听第二个实例启动的事件
        if (win) {
            if (win.isMinimized()) win.restore();
            if (!win.isVisible()) win.show();
            win.focus(); // 当运行第二个实例时,将会聚焦到第一个实例
        }
    });
}

module.exports = {
    getChatGPTUrl,
    onCloseBrowser,
    onSecondInstanceStart
};